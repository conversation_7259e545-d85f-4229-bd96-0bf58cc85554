<template>
  <button class="chip" :class="{ active: isActive }">
    <Text variant="label-medium">
      <slot />
    </Text>
  </button>
</template>

<script setup lang="ts">
import Text from '../text.vue';

withDefaults(defineProps<{ filter: string; isActive: boolean }>(), {
  isActive: false,
});
</script>

<style scoped lang="scss">
.chip {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--t-spacing-md);

  font-size: 0.875rem;
  font-weight: 500;

  line-height: 1.25rem;
  padding: 0.5rem;
  width: fit-content;
  height: 2rem;
  white-space: nowrap;
  border: none;
  background: transparent;
  appearance: none;
  color: #84857c;
  border: 1px solid transparent;
  border-radius: var(--t-border-radius-sm);

  &:hover,
  &:focus {
    cursor: pointer;
    background: #ebebe9;
    border-color: #ebebe9;
  }

  &:focus {
    border-color: var(--t-color-neutral-900);
    outline: none;
  }

  &.active {
    background: #ebebe9;
    border-color: #ebebe9;
    color: var(--t-text-color-primary);
  }
}
</style>
