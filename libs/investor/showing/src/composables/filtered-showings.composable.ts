import { computed } from 'vue';
import { useRoute } from 'vue-router';

import { getStartDateByFilter, TimeFilter } from '@tallo/utility';

import { ShowingFilters } from '../enums/showing-filters.enum';
import { RescheduleRequestStatus } from '../interfaces/reschedule-request.interface';
import { ShowingRequestStatus } from '../interfaces/showing-request-status.enum';
import { ShowingStatus } from '../interfaces/showing-status.enum';
import { useShowingsQuery } from '../queries/showings.query';

export function useFilteredShowings() {
  const route = useRoute();
  const { state } = useShowingsQuery();

  const propertyId = computed(() => route.params.propertyId as string);
  const selectedFilter = computed(() => route.query.filter as ShowingFilters);
  const selectedTimeFilter = computed(() => route.query.timeFilter as TimeFilter);
  const showings = computed(() => state.value.data || []);
  const showingList = computed(() => {
    switch (selectedFilter.value) {
      case ShowingFilters.ALL:
        return showings.value;

      case ShowingFilters.CONFIRMED:
        return showings.value.filter(({ status }) => status === ShowingStatus.CONFIRMED);

      case ShowingFilters.PENDING:
        return showings.value.filter((showing) => {
          const hasPendingShowingRequests = showing.showingRequests.some(
            ({ status }) => status === ShowingRequestStatus.PENDING,
          );

          const hasPendingRescheduleRequests = showing.rescheduleRequests.some(
            ({ status }) => status === RescheduleRequestStatus.PENDING,
          );

          return hasPendingShowingRequests || hasPendingRescheduleRequests;
        });

      case ShowingFilters.COMPLETED:
        return showings.value.filter(({ status }) => status === ShowingStatus.COMPLETED);

      case ShowingFilters.DECLINED:
        return showings.value.filter(({ status }) => status === ShowingStatus.DECLINED);

      case ShowingFilters.CANCELED:
        return showings.value.filter(({ status }) => status === ShowingStatus.CANCELED);

      case ShowingFilters.EXPIRED:
        return showings.value.filter(({ status }) => status === ShowingStatus.EXPIRED);

      default:
        return showings.value;
    }
  });

  const filteredShowings = computed(() => {
    let result = showingList.value;

    if (propertyId.value) {
      result = result.filter(({ property }) => property.id === propertyId.value);
    }

    if (selectedTimeFilter.value && selectedTimeFilter.value !== TimeFilter.ALL_TIME) {
      const startDate = getStartDateByFilter(selectedTimeFilter.value);

      result = result.filter(({ createdAt }) => new Date(createdAt) >= startDate);
    }

    return result.sort((a, b) => new Date(a.startTime).getTime() - new Date(b.startTime).getTime());
  });

  return {
    state,
    filteredShowings,
  };
}
