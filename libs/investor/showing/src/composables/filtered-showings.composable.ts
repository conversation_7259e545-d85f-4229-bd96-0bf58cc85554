import { computed } from 'vue';
import { useRoute } from 'vue-router';

import { getStartDateByFilter, TimeFilter } from '@tallo/utility';

import { ShowingFilters } from '../enums/showing-filters.enum';
import { RescheduleRequestStatus } from '../interfaces/reschedule-request.interface';
import { ShowingRequestStatus } from '../interfaces/showing-request-status.enum';
import { ShowingStatus } from '../interfaces/showing-status.enum';
import { useShowingsQuery } from '../queries/showings.query';

export function useFilteredShowings() {
  const route = useRoute();
  const { state } = useShowingsQuery();

  const propertyId = computed(() => route.params.propertyId as string);
  const selectedFilter = computed(() => route.query.filter as ShowingFilters);
  const selectedTimeFilter = computed(() => route.query.timeFilter as TimeFilter);
  const showings = computed(() => state.value.data || []);
  const showingList = computed(() => {
    switch (selectedFilter.value) {
      case ShowingFilters.ALL:
        return showings.value;

      case ShowingFilters.CONFIRMED:
        return showings.value.filter(({ status }) => status === ShowingStatus.CONFIRMED);

      case ShowingFilters.PENDING:
        return showings.value.filter((showing) => {
          const hasPendingShowingRequests = showing.showingRequests.some(
            ({ status }) => status === ShowingRequestStatus.PENDING,
          );

          const hasPendingRescheduleRequests = showing.rescheduleRequests.some(
            ({ status }) => status === RescheduleRequestStatus.PENDING,
          );

          return hasPendingShowingRequests || hasPendingRescheduleRequests;
        });

      case ShowingFilters.COMPLETED:
        return showings.value.filter(({ status }) => status === ShowingStatus.COMPLETED);

      case ShowingFilters.DECLINED:
        return showings.value.filter(({ status }) => status === ShowingStatus.DECLINED);

      case ShowingFilters.CANCELED:
        return showings.value.filter(({ status }) => status === ShowingStatus.CANCELED);

      case ShowingFilters.EXPIRED:
        return showings.value.filter(({ status }) => status === ShowingStatus.EXPIRED);

      default:
        return showings.value;
    }
  });

  const filteredShowings = computed(() => {
    let result = showingList.value;

    if (propertyId.value) {
      result = result.filter(({ property }) => property.id === propertyId.value);
    }

    if (selectedTimeFilter.value && selectedTimeFilter.value !== TimeFilter.ALL_TIME) {
      const startDate = getStartDateByFilter(selectedTimeFilter.value);

      result = result.filter(({ createdAt }) => new Date(createdAt) >= startDate);
    }

    return result.sort((a, b) => {
      const dateA = new Date(a.startTime);
      const dateB = new Date(b.startTime);

      // Extract just the date part (without time) for comparison
      const dateOnlyA = new Date(dateA.getFullYear(), dateA.getMonth(), dateA.getDate());
      const dateOnlyB = new Date(dateB.getFullYear(), dateB.getMonth(), dateB.getDate());

      // First sort by date descending (most recent dates first)
      const dateDiff = dateOnlyB.getTime() - dateOnlyA.getTime();

      // If dates are the same, sort by time ascending (earliest times first)
      if (dateDiff === 0) {
        return dateA.getTime() - dateB.getTime();
      }

      return dateDiff;
    });
  });

  return {
    state,
    filteredShowings,
  };
}
