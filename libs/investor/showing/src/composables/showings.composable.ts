import { computed, MaybeRef, unref } from 'vue';

import { ShowingStatus } from '../interfaces/showing-status.enum';
import { useShowingsQuery } from '../queries/showings.query';
import { groupShowingByDateAndSort } from '../utils/group-showing-by-date.util';
import { getPendingShowings } from '../utils/pending-showings.util';

export function useShowings(propertyIdRef?: MaybeRef<string>) {
  const { data, ...rest } = useShowingsQuery();

  const showings = computed(() => {
    const allShowings = data.value || [];
    const propertyId = propertyIdRef ? unref(propertyIdRef) : null;
    const filteredShowings = propertyId
      ? allShowings.filter(({ property }) => propertyId === property.id)
      : allShowings;

    return filteredShowings.sort((a, b) => {
      const dateA = new Date(a.startTime);
      const dateB = new Date(b.startTime);

      // Extract just the date part (without time) for comparison
      const dateOnlyA = new Date(dateA.getFullYear(), dateA.getMonth(), dateA.getDate());
      const dateOnlyB = new Date(dateB.getFullYear(), dateB.getMonth(), dateB.getDate());

      // First sort by date descending (most recent dates first)
      const dateDiff = dateOnlyB.getTime() - dateOnlyA.getTime();

      // If dates are the same, sort by time ascending (earliest times first)
      if (dateDiff === 0) {
        return dateA.getTime() - dateB.getTime();
      }

      return dateDiff;
    });
  });
  const pendingShowings = computed(() => getPendingShowings(showings.value));
  const upcomingShowings = computed(() => showings.value.filter(({ status }) => status === ShowingStatus.CONFIRMED));
  const groupedByDateUpcomingShowings = computed(() => groupShowingByDateAndSort(upcomingShowings.value));

  return {
    ...rest,
    data,
    showings,
    pendingShowings,
    groupedByDateUpcomingShowings,
  };
}
