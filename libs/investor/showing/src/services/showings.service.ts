import axios from 'axios';

import { TimeFilter } from '@tallo/utility';

import { CancelShowingDto } from '../interfaces/cancel-showing.interface';
import { RescheduleShowingDto } from '../interfaces/reschedule-showing.interface';
import { RescheduledShowingDto } from '../interfaces/rescheduled-showing.dto';
import { ShowingDto } from '../interfaces/showing.interface';

class ShowingService {
  async get(showingId: string): Promise<ShowingDto> {
    const response = await axios.get(`showing/${showingId}`);

    return response.data;
  }

  async getShowingsByCompany(
    companyId: string,
    params: { filter?: TimeFilter; onlyWithPendingShowingReqests?: boolean } = {},
  ): Promise<ShowingDto[]> {
    const response = await axios.get(`showing/company/${companyId}`, { params });

    return response.data;
  }

  async reschedule(showingId: string, payload: RescheduleShowingDto): Promise<RescheduledShowingDto> {
    const response = await axios.post(`showing/${showingId}/reschedule`, payload);

    return response.data;
  }

  async cancel(showingId: string, payload: CancelShowingDto): Promise<void> {
    await axios.post(`showing/${showingId}/cancel`, payload);
  }

  async assignAgent(showingId: string, showingAgentId: string): Promise<void> {
    await axios.post(`showing/${showingId}/assign-showing-agent`, { showingAgentId });
  }
}

export default new ShowingService();
