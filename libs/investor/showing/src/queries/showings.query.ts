import { useQuery } from '@pinia/colada';

import { useCompanyQuery } from '@tallo/investor/company';

import showingsService from '../services/showings.service';

export const showingsQueryKey = 'showings';

export function useShowingsQuery({ onlyWithPendingShowingReqests }: { onlyWithPendingShowingReqests?: boolean } = {}) {
  const queryKey = [showingsQueryKey];

  if (onlyWithPendingShowingReqests) {
    queryKey.push('onlyWithPendingShowingReqests');
  }

  return useQuery({
    key: () => queryKey,
    query: async () => {
      const companyQuery = useCompanyQuery();
      const { data } = await companyQuery.refresh(true);

      return showingsService.getShowingsByCompany(data?.id as string, { onlyWithPendingShowingReqests });
    },
  });
}
