import { useDateFormat } from '@vueuse/core';

import { ShowingDto } from '../interfaces/showing.interface';
import { ShowingsGroupedByDate } from '../interfaces/showings-grouped-by-date.interface';

export function groupShowingByDateAndSort(showings: ShowingDto[]): ShowingsGroupedByDate[] {
  const showingsByDate: { [date: string]: ShowingDto[] } = {};

  showings.forEach((showing) => {
    const date = new Date(showing.startTime);
    const formattedDate = useDateFormat(date, 'YYYY-MM-DD').value;

    if (!showingsByDate[formattedDate]) {
      showingsByDate[formattedDate] = [];
    }

    showingsByDate[formattedDate].push(showing);
  });

  const sortedShowings = Object.entries(showingsByDate)
    .map(([date, showings]) => ({
      date: new Date(`${date}T00:00:00`),
      showings,
    }))
    .sort((a, b) => a.date.getTime() - b.date.getTime());

  return sortedShowings;
}
