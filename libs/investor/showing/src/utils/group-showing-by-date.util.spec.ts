import { groupShowingByDateAndSort } from './group-showing-by-date.util';
import { ShowingDto } from '../interfaces/showing.interface';
import { ShowingStatus } from '../interfaces/showing-status.enum';
import { TourType } from '../interfaces/tour-type.enum';

// Mock useDateFormat since it's from @vueuse/core
jest.mock('@vueuse/core', () => ({
  useDateFormat: (date: Date, format: string) => ({
    value: date.toISOString().split('T')[0], // Returns YYYY-MM-DD format
  }),
}));

describe('groupShowingByDateAndSort', () => {
  const createMockShowing = (startTime: string, id: string = Math.random().toString()): ShowingDto => ({
    id,
    startTime: new Date(startTime),
    endTime: new Date(startTime),
    createdAt: new Date(startTime),
    status: ShowingStatus.CONFIRMED,
    showingRequests: [],
    rescheduleRequests: [],
    property: { id: 'prop1', displayName: 'Test Property' } as any,
    tourType: TourType.IN_PERSON,
  });

  it('should sort dates in descending order (most recent first)', () => {
    const showings = [
      createMockShowing('2024-01-01T10:00:00Z', '1'),
      createMockShowing('2024-01-03T10:00:00Z', '2'),
      createMockShowing('2024-01-02T10:00:00Z', '3'),
    ];

    const result = groupShowingByDateAndSort(showings);

    expect(result).toHaveLength(3);
    expect(result[0].date.toISOString().split('T')[0]).toBe('2024-01-03');
    expect(result[1].date.toISOString().split('T')[0]).toBe('2024-01-02');
    expect(result[2].date.toISOString().split('T')[0]).toBe('2024-01-01');
  });

  it('should sort times within the same date in ascending order (earliest first)', () => {
    const showings = [
      createMockShowing('2024-01-01T15:00:00Z', '1'),
      createMockShowing('2024-01-01T09:00:00Z', '2'),
      createMockShowing('2024-01-01T12:00:00Z', '3'),
    ];

    const result = groupShowingByDateAndSort(showings);

    expect(result).toHaveLength(1);
    expect(result[0].showings).toHaveLength(3);
    expect(result[0].showings[0].id).toBe('2'); // 09:00
    expect(result[0].showings[1].id).toBe('3'); // 12:00
    expect(result[0].showings[2].id).toBe('1'); // 15:00
  });

  it('should handle mixed dates and times correctly', () => {
    const showings = [
      createMockShowing('2024-01-01T15:00:00Z', '1'),
      createMockShowing('2024-01-02T09:00:00Z', '2'),
      createMockShowing('2024-01-01T09:00:00Z', '3'),
      createMockShowing('2024-01-02T15:00:00Z', '4'),
    ];

    const result = groupShowingByDateAndSort(showings);

    expect(result).toHaveLength(2);
    
    // First group should be 2024-01-02 (most recent date)
    expect(result[0].date.toISOString().split('T')[0]).toBe('2024-01-02');
    expect(result[0].showings).toHaveLength(2);
    expect(result[0].showings[0].id).toBe('2'); // 09:00
    expect(result[0].showings[1].id).toBe('4'); // 15:00
    
    // Second group should be 2024-01-01
    expect(result[1].date.toISOString().split('T')[0]).toBe('2024-01-01');
    expect(result[1].showings).toHaveLength(2);
    expect(result[1].showings[0].id).toBe('3'); // 09:00
    expect(result[1].showings[1].id).toBe('1'); // 15:00
  });
});
