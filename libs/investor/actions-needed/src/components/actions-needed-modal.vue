<template>
  <Modal position="end" ref="modalDialogRef">
    <template #header>
      <span>Actions needed</span>
      <Text variant="inherit" as="span" color="tertiary">&nbsp;({{ data.length }})</Text>
    </template>
    <template #body>
      <View v-if="isPending" justify="center" padding="xl">
        <LoadingIndicator />
      </View>

      <View v-else-if="data.length === 0" justify="center" align="center" padding="xl" direction="column" gap="sm">
        <Text variant="body-medium" align="center">No actions needed right now</Text>
        <Text variant="body-small" color="tertiary" align="center">
          New items requiring your attention will appear here
        </Text>
      </View>

      <ActionsNeededList v-else :items="data" />
    </template>
  </Modal>
</template>

<script setup lang="ts">
import { ref } from 'vue';

import { LoadingIndicator, Modal, Text, View } from '@tallo/design-system';

import { useActionsNeeded } from '../composables/actions-needed.composable';
import ActionsNeededList from './actions-needed-list.vue';

const modalDialogRef = ref<InstanceType<typeof Modal>>();

const { data, isPending } = useActionsNeeded();

function open() {
  modalDialogRef.value?.show();
}

defineExpose({ open });
</script>

<style scoped lang="scss"></style>
