<template>
  <Card size="medium" variant="tertiary" class="activity-feed-item-card">
    <component :is="link ? RouterLink : 'div'" :to="link">
      <View gap="sm">
        <ActivityFeedItemIcon :event-name="item.eventName" class="activity-feed-icon" />

        <View direction="column" :grow="1" class="action-info">
          <Text variant="label-medium">{{ item.eventName }}</Text>
          <Text variant="body-medium" color="secondary" class="action-info-text">
            {{ item.property?.displayName }} &middot; {{ item.user?.name }}
          </Text>
        </View>

        <Text variant="body-small" color="tertiary" align="end" wrap="nowrap">
          <div>{{ date }},</div>
          <div>{{ time }}</div>
        </Text>
      </View>
    </component>
  </Card>
</template>

<script setup lang="ts">
import { useDateFormat } from '@vueuse/core';
import { computed } from 'vue';
import { RouterLink } from 'vue-router';

import { Card, Text, View } from '@tallo/design-system';
import { propertyRoute, renterProfileRoute, showingDetailsRoute } from '@tallo/investor/navigation';

import { ActivityFeedEventName } from '../enums/activity-feed-event-name.enum';
import { ActivityFeedItemDto } from '../interfaces/activity-feed-item.dto';
import ActivityFeedItemIcon from './activity-feed-item-icon.vue';

const props = defineProps<{ item: ActivityFeedItemDto }>();
const time = computed(() => useDateFormat(props.item.createdAt, 'hh:mm a').value);
const date = computed(() => {
  const date = new Date(props.item.createdAt);
  const now = new Date();

  const isSameDay = (a: Date, b: Date) => {
    return a.getFullYear() === b.getFullYear() && a.getMonth() === b.getMonth() && a.getDate() === b.getDate();
  };

  const isYesterday = (d: Date) => {
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    return isSameDay(d, yesterday);
  };

  if (isSameDay(date, now)) {
    return 'Today';
  }

  if (isYesterday(date)) {
    return 'Yesterday';
  }

  return useDateFormat(props.item.createdAt, 'MMM D').value;
});

const link = computed(() => {
  switch (props.item.eventName) {
    case ActivityFeedEventName.PROPERTY_ADDED:
    case ActivityFeedEventName.PROPERTY_LISTED:
    case ActivityFeedEventName.PROPERTY_RENTED_OUT:
      return props.item.property ? propertyRoute(props.item.property?.id) : null;

    case ActivityFeedEventName.APPLICATION_SENT:
    case ActivityFeedEventName.APPLICATION_REQUESTED:
    case ActivityFeedEventName.APPLICATION_COMPLETED:
    case ActivityFeedEventName.SHOWING_REQUESTED:
    case ActivityFeedEventName.NEW_LEAD:
    case ActivityFeedEventName.LEAD_QUALIFIED:
      return props.item.property && props.item.renter
        ? renterProfileRoute(props.item.property?.id, props.item.renter?.id)
        : null;

    case ActivityFeedEventName.SHOWING_CONFIRMED:
    case ActivityFeedEventName.SHOWING_COMPLETED:
    case ActivityFeedEventName.SHOWING_ATTENDANCE_CONFIRMED:
    case ActivityFeedEventName.SHOWING_ATTENDANCE_DECLINED:
      return props.item.showing ? showingDetailsRoute(props.item.showing?.id) : null;

    default:
      return null;
  }
});
</script>

<style scoped lang="scss">
.activity-feed-item-card {
  flex-grow: 1;
}

.activity-feed-icon {
  flex-shrink: 0;
}

.action-info {
  overflow: hidden;
}

.action-info-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
