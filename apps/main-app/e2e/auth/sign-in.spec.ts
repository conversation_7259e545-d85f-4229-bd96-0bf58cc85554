import { test, expect } from '@playwright/test';

test.describe('Sign In', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
  });

  test('should redirect to sign-in page', async ({ page }) => {
    await expect(page).toHaveURL('auth/sign-in');
    await expect(page.getByRole('heading', { name: 'Welcome back' })).toBeVisible();
    await expect(page.getByText('Log in to start using the app')).toBeVisible();
    await expect(page.getByLabel('Email')).toBeVisible();
    await expect(page.getByLabel('Password')).toBeVisible();
    await expect(page.getByRole('button', { name: 'Log in' })).toBeVisible();
  });

  ['<EMAIL>', process.env['E2E_USER_EMAIL']!].forEach((email) => {
    test(`should display error when email or password invalid: ${email}`, async ({ page }) => {
      await page.getByLabel('Email').fill(email);
      await page.getByLabel('Password').fill('invalid-password');
      await page.getByRole('button', { name: 'Log in' }).click();

      await expect(page.getByText('Invalid email or password')).toBeVisible();
    });
  });

  test('should successfully log in with valid credentials', async ({ page }) => {
    await page.getByLabel('Email').fill(process.env['E2E_USER_EMAIL']!);
    await page.getByLabel('Password').fill(process.env['E2E_USER_PASSWORD']!);
    await page.getByRole('button', { name: 'Log in' }).click();

    await expect(page).toHaveURL('/');
    await expect(page.getByRole('heading', { name: 'Performance' })).toBeVisible();
  });
});
