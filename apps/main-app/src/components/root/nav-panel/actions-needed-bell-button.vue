<template>
  <div class="bell-button-container">
    <IconButton @click="openModal" variant="ghost" icon="bell-01" size="large" shape="round" class="bell-button" />
    <div v-if="hasActionsNeeded" class="notification-dot"></div>

    <ActionsNeededModal ref="actionsNeededModalRef" />
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';

import { IconButton } from '@tallo/design-system';
import { ActionsNeededModal, useActionsNeeded } from '@tallo/investor/actions-needed';

const { data } = useActionsNeeded();
const actionsNeededModalRef = ref<InstanceType<typeof ActionsNeededModal>>();

const hasActionsNeeded = computed(() => data.value.length > 0);

function openModal() {
  actionsNeededModalRef.value?.open();
}
</script>

<style scoped lang="scss">
.bell-button-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.bell-button {
  background-color: #fff;
  border-radius: 50%;
  color: #8b8e8f;
}

.notification-dot {
  position: absolute;
  top: 0.75rem;
  right: 0.875rem;
  width: 0.625rem;
  height: 0.625rem;
  background-color: #f66b22;
  border: 2px solid var(--t-color-neutral-white);
  border-radius: 50%;
}
</style>
