<template>
  <dialog class="menu" ref="dialogRef" @click="close" @keyup.esc="close">
    <div class="content" @click.stop>
      <div class="top-bar">
        <IconButton @click.stop="close" variant="ghost" icon="x-close" />
      </div>

      <View class="profile-section">
        <router-link :to="profileRoute()" class="profile-link" @click="close">
          <View gap="md" align="center">
            <Avatar v-if="investor?.user" :userName="investor.user.name" size="large" />
            <View direction="column" justify="center" gap="xs">
              <Text variant="h4">{{ investor?.user?.name }}</Text>
              <Text variant="label-small" color="tertiary">{{ investor?.user?.email }}</Text>
            </View>
          </View>
        </router-link>
      </View>

      <nav class="navigation">
        <router-link v-for="link in mainNavigationConfig" :key="link.title" :to="{ name: link.appRoute }" class="link">
          {{ link.title }}
        </router-link>
      </nav>
    </div>
  </dialog>
</template>

<script setup lang="ts">
import { storeToRefs } from 'pinia';
import { ref } from 'vue';
import { useRouter } from 'vue-router';

import { Avatar, IconButton, Text, View } from '@tallo/design-system';
import { useInvestorStore } from '@tallo/investor/investor';
import { mainNavigationConfig, profileRoute } from '@tallo/investor/navigation';

const investorStore = useInvestorStore();
const { investor } = storeToRefs(investorStore);

const opened = ref(false);
const dialogRef = ref<HTMLDialogElement>();

const open = () => {
  opened.value = true;
  dialogRef.value?.show();
};

const close = () => {
  opened.value = false;
  dialogRef.value?.close();
};

useRouter().beforeEach(() => {
  if (opened.value) {
    close();
  }
});

defineExpose({
  open,
  close,
});
</script>

<style scoped lang="scss">
.menu {
  width: 100%;
  height: 100vh;
  border: none;
  padding: 0;
  top: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.content {
  background-color: white;
}

.top-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 1rem;
  height: var(--t-header-height-mobile);
}

.navigation {
  display: flex;
  flex-direction: column;
  padding: 0 1.5rem 2rem;

  font-size: 1rem;
  font-weight: 500;
  line-height: 1.5rem;
}

.link {
  padding: 0.75rem 0.5rem;
  border-radius: var(--t-border-radius-sm);

  &.router-link-active {
    background-color: #f5f5f5;
  }
}

.profile-section {
  margin-bottom: var(--t-spacing-md);
  padding: 0.75rem 1.5rem;
}

.profile-link {
  display: block;
  text-decoration: none;
  border-radius: var(--t-border-radius-sm);
}
</style>
