<template>
  <PlaceholderSection
    v-if="isLoading || sortedEscalations.length === 0"
    :is-loading="isLoading"
    :icon="'calendar-question'"
    title="No escalations to review"
    description="When renters have questions or need assistance, intelligent escalations will appear here for your attention."
  />

  <div v-else class="intelligent-escalations">
    <IntelligentEscalationCard
      v-for="escalation in sortedEscalations"
      :key="escalation.id"
      :question="escalation"
      @click="showModal(escalation)"
    />
  </div>

  <IntelligentEscalationModal
    v-if="selectedEscalation"
    :escalation="selectedEscalation"
    @closed="resetSelectedEscalation"
    ref="intelligentEscalationModalRef"
  />
</template>

<script setup lang="ts">
import { computed, nextTick, ref } from 'vue';

import {
  IntelligentEscalationCard,
  IntelligentEscalationDto,
  IntelligentEscalationModal,
} from '@tallo/investor/intelligent-escalations';

import PlaceholderSection from '../placeholder-section.vue';

interface Props {
  isLoading: boolean;
  escalations: IntelligentEscalationDto[];
}

const props = defineProps<Props>();
const intelligentEscalationModalRef = ref<InstanceType<typeof IntelligentEscalationModal>>();
const selectedEscalation = ref<IntelligentEscalationDto>();

const sortedEscalations = computed(() => {
  return props.escalations.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
});

function showModal(escalation: IntelligentEscalationDto) {
  selectedEscalation.value = escalation;

  nextTick(() => {
    intelligentEscalationModalRef.value?.open();
  });
}

function resetSelectedEscalation() {
  selectedEscalation.value = undefined;
}
</script>

<style scoped lang="scss">
.intelligent-escalations {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(20rem, 1fr));
  gap: var(--t-spacing-md);
}
</style>
