<template>
  <View gap="md" direction="column" class="applications-section">
    <TopBarFilters :filters="filters" :selectedFilter="selectedFilter" @selected="selectedFilter = $event" />

    <PlaceholderSection
      v-if="isLoading || bundles.length === 0"
      :is-loading="isLoading"
      :icon="'file-question-02'"
      :title="getEmptyStateText()"
      :description="getEmptyStateDescription()"
    />

    <div v-else class="applications">
      <ApplicationBundleCard
        v-for="applicationBundle in bundles"
        :key="applicationBundle.id"
        :applicationBundle="applicationBundle"
      />
    </div>
  </View>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';

import { ApplicationBundleStatus } from '@tallo/applications';
import { TopBarFilters, View } from '@tallo/design-system';
import { ApplicationBundleCard } from '@tallo/investor/applications';

import PlaceholderSection from '../placeholder-section.vue';

enum ApplicationBundleFilter {
  READY_FOR_REVIEW = 'Ready for review',
  IN_PROGRESS = 'In progress',
}

interface Props {
  isLoading: boolean;
  applicationBundles: any[];
}

const props = defineProps<Props>();

const filters = Object.values(ApplicationBundleFilter);
const selectedFilter = ref(filters[0]);

const bundles = computed(() =>
  props.applicationBundles
    .filter((bundle) => {
      switch (selectedFilter.value) {
        case ApplicationBundleFilter.READY_FOR_REVIEW:
          return bundle.status === ApplicationBundleStatus.SUBMITTED;

        case ApplicationBundleFilter.IN_PROGRESS:
          return [ApplicationBundleStatus.SENT, ApplicationBundleStatus.IN_PROGRESS].includes(bundle.status);
      }
    })
    .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()),
);

function getEmptyStateText(): string {
  switch (selectedFilter.value) {
    case ApplicationBundleFilter.READY_FOR_REVIEW:
      return 'No applications ready for review';
    case ApplicationBundleFilter.IN_PROGRESS:
      return 'No applications in progress';
    default:
      return 'No applications available';
  }
}

function getEmptyStateDescription(): string {
  switch (selectedFilter.value) {
    case ApplicationBundleFilter.READY_FOR_REVIEW:
      return 'Applications that are ready for your review will appear here once renters submit them.';
    case ApplicationBundleFilter.IN_PROGRESS:
      return 'Applications that are currently being processed will appear here.';
    default:
      return 'Applications will appear here once renters start applying to your properties.';
  }
}
</script>

<style scoped lang="scss">
.applications-section {
  height: 100%;
}

.applications {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(20rem, 1fr));
  gap: var(--t-spacing-md);
}
</style>
