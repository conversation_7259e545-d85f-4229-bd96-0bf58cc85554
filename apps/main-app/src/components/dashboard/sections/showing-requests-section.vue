<template>
  <PlaceholderSection
    v-if="isLoading || pendingShowings.length === 0"
    :is-loading="isLoading"
    :icon="'calendar-question'"
    title="No showing requests yet"
    description="When renters request showings for your properties, they'll appear here for you to review and schedule."
  />

  <div v-else class="showings">
    <ShowingCard v-for="showing in pendingShowings" :key="showing.id" :showing="showing" />
  </div>
</template>

<script setup lang="ts">
import { ShowingCard } from '@tallo/investor/showing';

import PlaceholderSection from '../placeholder-section.vue';

interface Props {
  isLoading: boolean;
  pendingShowings: any[];
}

defineProps<Props>();
</script>

<style scoped lang="scss">
.showings {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(20rem, 1fr));
  gap: var(--t-spacing-md);
}
</style>
