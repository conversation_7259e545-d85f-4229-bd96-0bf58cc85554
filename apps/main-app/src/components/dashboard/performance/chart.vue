<template>
  <div class="chart">
    <Line v-if="data" :data="chartData" :options="options" :plugins="plugins" />
  </div>
</template>

<script setup lang="ts">
import { useDateFormat } from '@vueuse/core';
import {
  Chart as ChartJS,
  Title,
  Tooltip,
  Legend,
  CategoryScale,
  LinearScale,
  LineElement,
  PointElement,
  Filler,
  ChartOptions,
  ChartData,
  Plugin,
} from 'chart.js';
import { computed } from 'vue';
import { Line } from 'vue-chartjs';

import { PortfolioTimeSeriesGranularity, PortfolioTimeSeriesDto } from '@tallo/investor/portfolio-statistics';

import { StatisticsConfigName, statisticsConfigs } from './statistics-config';

ChartJS.register(CategoryScale, LinearScale, LineElement, PointElement, Title, Tooltip, Legend, Filler);

ChartJS.defaults.font = {
  size: 12,
  family: 'Inter, sans-serif',
};

const props = defineProps<{
  configName: StatisticsConfigName;
  timeSeriesData: PortfolioTimeSeriesDto;
}>();
const selectedConfig = computed(() => statisticsConfigs.get(props.configName));
const selectedConfigApiFields = computed(() => selectedConfig.value?.apiFields || []);
const data = computed(() => props.timeSeriesData);

const chartLabels = computed(() => {
  if (!data.value) {
    return [];
  }

  const dataPoints = data.value.dataPoints;
  const granularity = data.value.granularity;

  return dataPoints.map((dataPoint) => {
    switch (granularity) {
      case PortfolioTimeSeriesGranularity.HOUR:
        return useDateFormat(dataPoint.timestamp, 'ha').value;
      case PortfolioTimeSeriesGranularity.DAY:
        return useDateFormat(dataPoint.timestamp, 'MMM D').value;
      case PortfolioTimeSeriesGranularity.WEEK:
        return useDateFormat(dataPoint.timestamp, 'MMM D').value;
      case PortfolioTimeSeriesGranularity.MONTH:
        return useDateFormat(dataPoint.timestamp, 'MMM YYYY').value;
      default:
        return dataPoint.timestamp;
    }
  });
});

const chartData = computed<ChartData<'line'>>(() => {
  if (!data.value) {
    return {
      datasets: [],
    };
  }

  const dataPoints = data.value.dataPoints;

  return {
    labels: chartLabels.value,
    datasets: [
      {
        label: selectedConfigApiFields.value[0]?.fullLabel,
        data: dataPoints.map((dataPoint) => dataPoint[selectedConfigApiFields.value[0]?.key]),
        borderColor: '#FFA800',
        pointBackgroundColor: '#FFA800',
        backgroundColor: (context) => {
          const chart = context.chart;
          const { ctx, chartArea } = chart;

          if (!chartArea) {
            return;
          }

          const gradient = ctx.createLinearGradient(0, 0, 0, 300);
          gradient.addColorStop(0, 'rgba(255, 168, 0, 0.1)');
          gradient.addColorStop(1, 'rgba(255, 168, 0, 0.03)');

          return gradient;
        },
        tension: 0.4,
        fill: 1,
        borderWidth: 2,
      },
      {
        label: selectedConfigApiFields.value[1]?.fullLabel,
        data: dataPoints.map((dataPoint) => dataPoint[selectedConfigApiFields.value[1]?.key]),
        borderColor: '#F86A22',
        backgroundColor: 'rgba(0, 0, 0, 0)',
        fill: true,
        tension: 0.4,
        borderWidth: 2,
        pointBackgroundColor: '#F86A22',
      },
    ],
  };
});

const options: ChartOptions<'line'> = {
  responsive: true,
  maintainAspectRatio: false,
  interaction: {
    mode: 'index',
    intersect: false,
    axis: 'x',
  },
  plugins: {
    tooltip: {
      mode: 'index',
      intersect: false,
    },
    legend: {
      position: 'top',
      align: 'start',
      labels: {
        color: '#A7A8A0',
        usePointStyle: true,
        boxWidth: 8,
        boxHeight: 8,
        pointStyle: 'rectRounded',
      },
    },
    filler: {
      propagate: false,
    },
  },
  elements: {
    point: {
      radius: 0,
    },
  },
  scales: {
    x: {
      grid: {
        display: false,
      },
    },
    y: {
      grid: {
        color: '#E5E7EB',
      },
      min: 0,
      ticks: {
        stepSize: 1,
        color: '#6B7280',
      },
    },
  },
};

const plugins: Plugin<'line'>[] = [
  {
    id: 'verticalLine',
    afterDraw: (chart) => {
      if (chart.tooltip && chart.tooltip.getActiveElements().length) {
        const ctx = chart.ctx;
        const activeElements = chart.tooltip.getActiveElements();
        const activePoint = activeElements[0];
        const x = activePoint.element.x;
        const topY = chart.chartArea.top;
        const bottomY = chart.chartArea.bottom;

        ctx.save();
        ctx.beginPath();
        ctx.moveTo(x, topY);
        ctx.lineTo(x, bottomY);
        ctx.lineWidth = 1;
        ctx.strokeStyle = 'rgba(0,0,0,0.2)';
        ctx.stroke();
        ctx.restore();
      }
    },
  },
  {
    id: 'legendPaddingPlugin',
    beforeInit(chart) {
      if (chart.legend) {
        const originalFit = chart.legend.fit;

        chart.legend.fit = function fit() {
          originalFit.bind(chart.legend)();
          this.height += 8;
        };
      }
    },
  },
];
</script>

<style scoped lang="scss"></style>
