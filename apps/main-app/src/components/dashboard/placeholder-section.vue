<template>
  <Card variant="tertiary" class="card-placeholder">
    <View v-if="isLoading" justify="center" align="center" padding="xl">
      <LoadingIndicator />
    </View>
    <View v-else justify="center" align="center" padding-inline="xl" direction="column" gap="sm">
      <View justify="center" align="center" class="card-placeholder-icon">
        <Icon :name="icon" />
      </View>
      <Text variant="h5" align="center">{{ title }}</Text>
      <Text variant="body-medium" color="secondary" align="center">{{ description }}</Text>
    </View>
  </Card>
</template>

<script setup lang="ts">
import { Card, Icon, IconName, LoadingIndicator, Text, View } from '@tallo/design-system';

defineProps<{ title: string; description: string; icon: IconName; isLoading?: boolean }>();
</script>

<style scoped lang="scss">
.card-placeholder {
  height: 100%;
  align-content: center;
}

.card-placeholder-icon {
  width: 3rem;
  height: 3rem;

  color: #6a808f;
  background-color: #eaeef4;
  border-radius: 50%;
}
</style>
