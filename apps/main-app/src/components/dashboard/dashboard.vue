<template>
  <PageContainer height="full" class="dashboard-layout" :class="{ mobile: isMobile }">
    <!-- Desktop Layout -->
    <template v-if="!isMobile">
      <View gap="lg" direction="column" :grow="1" class="main-content">
        <DashboardPerformance />
        <DashboardSections />
      </View>
      <View gap="lg" direction="column" :shrink="0" class="aside">
        <DashboardActionsNeeded />
        <DashboardActivityFeed :limit="8" />
      </View>
    </template>
    <!-- Mobile Layout -->
    <View v-else gap="lg" direction="column" :grow="1" class="mobile-content">
      <TopBarFilters
        :filters="mobileFilters"
        :selectedFilter="selectedMobileTab"
        @selected="selectedMobileTab = $event"
        class="mobile-tabs"
      />

      <View v-if="selectedMobileTab === Tabs.MAIN" gap="lg" direction="column" :grow="1" class="mobile-tab-content">
        <DashboardPerformance />
        <DashboardActionsNeeded />
        <DashboardSections />
      </View>

      <View v-if="selectedMobileTab === Tabs.ACTIVITY_FEED" gap="lg" direction="column" class="mobile-tab-content">
        <DashboardActivityFeed />
      </View>
    </View>
  </PageContainer>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';

import { PageContainer, View, TopBarFilters } from '@tallo/design-system';

import DashboardActionsNeeded from './dashboard-actions-needed.vue';
import DashboardActivityFeed from './dashboard-activity-feed.vue';
import DashboardPerformance from './dashboard-performance.vue';
import DashboardSections from './dashboard-sections.vue';

enum Tabs {
  MAIN = 'Main',
  ACTIVITY_FEED = 'Activity feed',
}

const isMobile = ref(false);
const mobileFilters = Object.values(Tabs);
const selectedMobileTab = ref(Tabs.MAIN);

function checkScreenSize() {
  isMobile.value = window.innerWidth < 1024;
}

onMounted(() => {
  checkScreenSize();
  window.addEventListener('resize', checkScreenSize);
});

onUnmounted(() => {
  window.removeEventListener('resize', checkScreenSize);
});
</script>

<style scoped lang="scss">
.dashboard-layout {
  --gap: var(--t-spacing-xl);
  --aside-width: 25rem;

  display: flex;
  gap: var(--gap);

  .main-content {
    display: flex;
    gap: var(--t-spacing-md);
    max-width: calc(100% - var(--aside-width) - var(--gap));
    width: calc(100% - var(--aside-width) - var(--gap));
  }

  .aside {
    width: var(--aside-width);
  }

  &.mobile {
    flex-direction: column;
    gap: 0;
  }
}

.mobile-content,
.mobile-tab-content {
  height: 100%;
}
</style>
