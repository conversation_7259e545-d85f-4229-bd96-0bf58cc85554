<template>
  <View gap="md" direction="column" :grow="1">
    <View gap="md" padding-block="sm" role="tablist" class="tabs">
      <Text
        variant="h3"
        as="button"
        class="section-tab"
        role="tab"
        wrap="nowrap"
        :tabindex="0"
        :aria-selected="activeSection === section.label"
        v-for="section in sections"
        :color="activeSection === section.label ? 'primary' : 'tertiary'"
        @click="onSelectTab(section.label)"
      >
        <div>{{ section.label }}</div>
        <div class="indicator" :class="{ active: section.indicator }"></div>
      </Text>
    </View>

    <div class="section-content">
      <ShowingRequestsSection
        v-if="activeSection === Section.SHOWING_REQUESTS"
        :is-loading="showingsQuery.isPending.value"
        :pending-showings="pendingShowings"
      />

      <IntelligentEscalationsSection
        v-if="activeSection === Section.INTELLIGENT_ESCALATIONS"
        :is-loading="intelligentEscalationsQuery.isPending.value"
        :escalations="intelligentEscalations"
      />

      <ApplicationsSection
        v-if="activeSection === Section.APPLICATIONS"
        :is-loading="applicationBundlesLoading"
        :application-bundles="applicationBundles"
      />
    </div>
  </View>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';

import { ApplicationBundleStatus } from '@tallo/applications';
import { Text, View } from '@tallo/design-system';
import { useApplicationBundles } from '@tallo/investor/applications';
import { useIntelligentEscalationsQuery } from '@tallo/investor/intelligent-escalations';
import { useShowingsQuery } from '@tallo/investor/showing';

import ApplicationsSection from './sections/applications-section.vue';
import IntelligentEscalationsSection from './sections/intelligent-escalations-section.vue';
import ShowingRequestsSection from './sections/showing-requests-section.vue';

enum Section {
  INTELLIGENT_ESCALATIONS = 'Intelligent escalations',
  SHOWING_REQUESTS = 'Showing requests',
  APPLICATIONS = 'Applications',
}

const showingsQuery = useShowingsQuery({ onlyWithPendingShowingReqests: true });
const intelligentEscalationsQuery = useIntelligentEscalationsQuery();
const { applicationBundles, isPending: applicationBundlesLoading } = useApplicationBundles();

const pendingShowings = computed(() => showingsQuery.data.value || []);
const intelligentEscalations = computed(() => intelligentEscalationsQuery.data?.value || []);
const filteredApplicationBundles = computed(() =>
  applicationBundles.value.filter((bundle) => {
    return [
      ApplicationBundleStatus.SUBMITTED,
      ApplicationBundleStatus.SENT,
      ApplicationBundleStatus.IN_PROGRESS,
    ].includes(bundle.status);
  }),
);

const sections = computed(() => [
  {
    label: Section.INTELLIGENT_ESCALATIONS,
    indicator: intelligentEscalations.value.length > 0,
  },
  {
    label: Section.SHOWING_REQUESTS,
    indicator: pendingShowings.value.length > 0,
  },
  {
    label: Section.APPLICATIONS,
    indicator: filteredApplicationBundles.value.length > 0,
  },
]);
const activeSection = ref(sections.value[0].label);

function onSelectTab(tab: Section) {
  activeSection.value = tab;
}
</script>

<style scoped lang="scss">
.tabs {
  overflow-x: auto;
  scrollbar-width: none;

  &::-webkit-scrollbar {
    display: none;
  }
}

.indicator {
  width: var(--t-spacing-sm);
  height: var(--t-spacing-sm);
  border-radius: 50%;
  background-color: transparent;

  &.active {
    background-color: #f66b22;
  }
}

.section-tab {
  display: flex;
  gap: var(--t-spacing-sm);
  align-items: center;
  padding: 0;
  cursor: pointer;
  appearance: none;
  border: none;
  background: transparent;
}

.section-content {
  height: 100%;
}
</style>
