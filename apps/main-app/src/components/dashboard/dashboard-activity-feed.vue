<template>
  <View gap="sm" direction="column" :grow="1">
    <View gap="md" direction="row" justify="space-between" padding-block="sm">
      <Text variant="h3">Activity feed</Text>
      <Text
        as="button"
        variant="body-large"
        color="secondary"
        v-if="hasMoreDataThanLimit"
        class="see-all"
        @click="openModal"
      >
        See all
      </Text>
    </View>

    <PlaceholderSection
      v-if="isPending || data.length === 0"
      :is-loading="isPending"
      :icon="'clock-rewind'"
      title="No recent activity"
      description="Activity from your properties and applications will appear here"
    />

    <ActivityFeedList v-else :items="items" />

    <ActivityFeedModal ref="activityFeedModalRef" />
  </View>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';

import { Text, View } from '@tallo/design-system';
import { useActivityFeedQuery, ActivityFeedModal, ActivityFeedList } from '@tallo/investor/activity-feed';

import PlaceholderSection from './placeholder-section.vue';

const props = defineProps<{ limit?: number }>();
const activityFeedQuery = useActivityFeedQuery();
const data = computed(() => activityFeedQuery.data?.value || []);
const isPending = computed(() => activityFeedQuery.isPending.value);
const items = computed(() => (props.limit ? data.value.slice(0, props.limit) : data.value));
const hasMoreDataThanLimit = computed(() => (props.limit ? data.value.length > props.limit : false));
const activityFeedModalRef = ref<InstanceType<typeof ActivityFeedModal>>();

function openModal() {
  activityFeedModalRef.value?.open();
}
</script>

<style scoped lang="scss">
.see-all {
  appearance: none;
  border: none;
  background: none;

  &:hover {
    cursor: pointer;
    text-decoration: underline;
  }
}
</style>
