<template>
  <View class="dev-tools-chat-component" direction="column">
    <div ref="messagesContainer" class="bubbles-window">
      <View
        v-for="(message, index) in messages"
        class="chat-bubble"
        padding="sm"
        :key="index"
        :class="{
          incoming: message.isSystem,
          outgoing: !message.isSystem,
          selected: devToolsStore.selectedMessages.includes(message),
        }"
        @click="handleMessageClick($event, message)"
      >
        {{ message.text }}
      </View>
      <View v-if="devToolsStore.sendingMessage" padding="sm" class="blinking">🧑🏻‍💻 Tallo is typing...</View>
      <View v-if="devToolsStore.creatingConvo && !devToolsStore.loadingConvos" padding="sm" class="blinking">
        🧑🏼‍🔧 Hold tight, spinning up a fresh convo...
      </View>
      <View v-if="devToolsStore.loadingConvos" padding="sm" class="blinking">📶 Looking for existing convos...</View>
    </div>
    <View class="chat-input" direction="row" padding="sm" gap="xs">
      <FormField class="input">
        <Input
          v-model="inputText"
          :disabled="userWantsToSendMessageUponReply"
          placeholder="Type a message..."
          @keyup.enter="onInputEnterPress"
          @change="saveMessageToLocalStorage"
        />
      </FormField>
      <IconButton icon="trash" v-if="devToolsStore.hasSelectedMessages" @click="devToolsStore.deleteSelectedMessages" />
      <IconButton
        icon="copy"
        v-if="messages.length > 0"
        @click="copyDialogForAI"
        title="Copy dialog for AI debugging"
      />
      <Button
        class="send-button"
        v-if="sendWhenTalloRespondsShown"
        :disabled="userWantsToSendMessageUponReply"
        @click="sendUponTalloReply"
      >
        {{ userWantsToSendMessageUponReply ? 'Waiting...' : 'Send Upon Reply' }}
      </Button>
      <Button class="send-button" v-else :disabled="sendButtonDisabled" @click="sendMessage">Send</Button>
    </View>
  </View>
  <View class="quick-messages" direction="column" gap="sm" padding-bottom="md" padding-inline="md">
    <Text as="div" :variant="'body-medium'">Quick Messages</Text>
    <Link v-for="quickMessage in quickMessagesCommon" :color="'accent'" @click="sendQuickMessage(quickMessage)">
      <Text :variant="'body-small'">{{ quickMessage }}</Text>
    </Link>
    <View gap="md" align="center" v-if="recentMessages.length > 0">
      <Text as="div" :variant="'body-medium'">Recent</Text>
      <IconButton @click="clearRecentMessages" icon="x-close" size="small" variant="ghost" />
    </View>
    <Link v-for="recentMessage in recentMessages" :color="'accent'" @click="sendQuickMessage(recentMessage)">
      <Text :variant="'body-small'">{{ recentMessage }}</Text>
    </Link>
  </View>
</template>

<script setup lang="ts">
import { useQueryCache } from '@pinia/colada';
import { onKeyStroke } from '@vueuse/core';
import { computed, nextTick, onMounted, onUnmounted, ref, watch } from 'vue';

import { Button, FormField, IconButton, Input, Link, Text, View, useAlertsStore } from '@tallo/design-system';
import { showingsQueryKey } from '@tallo/investor/showing';

import { useDevToolsStore } from './dev-tools.store';
import { DevToolsConvoMessage } from './model/dev-tools-convo-message.inteface';
import { DevToolsStorageKey } from './model/dev-tools-storage-key.enum';
import { quickMessagesCommon } from './utils/quick-messages.const';

const inputText = ref('');
const messages = ref<DevToolsConvoMessage[]>([]);
const messagesContainer = ref<HTMLElement | null>(null);
const recentMessages = ref<string[]>([]);

const devToolsStore = useDevToolsStore();
const alerts = useAlertsStore();
const queryCache = useQueryCache();

const sendButtonDisabled = computed(() => devToolsStore.sendingMessage || !devToolsStore.convoParamsExist);
const sendWhenTalloRespondsShown = computed(
  () => inputText.value && devToolsStore.sendingMessage && devToolsStore.convoParamsExist,
);
const userWantsToSendMessageUponReply = ref(false);

onMounted(() => {
  const savedMessage = localStorage.getItem(DevToolsStorageKey.MESSAGE_DRAFT);
  const savedRecentMessages = localStorage.getItem(DevToolsStorageKey.RECENT_MESSAGES);

  if (savedMessage) {
    inputText.value = savedMessage;
  }

  if (savedRecentMessages) {
    recentMessages.value = JSON.parse(savedRecentMessages);
  }
});

onUnmounted(() => {
  queryCache.invalidateQueries({ key: [showingsQueryKey] });
});

function handleMessageClick(event: MouseEvent, message: DevToolsConvoMessage): void {
  // Delete message on "CMD + OPTION + CLICK ON MESSAGE"
  if (event.metaKey && event.altKey && message.id) {
    devToolsStore.deleteSingleMessage(message);
  } else {
    devToolsStore.toggleMessageSelection(message);
  }
}

function saveMessageToLocalStorage(): void {
  localStorage.setItem(DevToolsStorageKey.MESSAGE_DRAFT, inputText.value);
}

function clearMessageFromLocalStorage(): void {
  localStorage.removeItem(DevToolsStorageKey.MESSAGE_DRAFT);
}

function saveRecentMessage(message: string): void {
  if (!quickMessagesCommon.includes(message) && !recentMessages.value.includes(message)) {
    recentMessages.value.unshift(message);

    if (recentMessages.value.length > 14) {
      recentMessages.value.pop();
    }

    localStorage.setItem(DevToolsStorageKey.RECENT_MESSAGES, JSON.stringify(recentMessages.value));
  }
}

function clearRecentMessages(): void {
  recentMessages.value = [];
  localStorage.removeItem(DevToolsStorageKey.RECENT_MESSAGES);
}

function sendQuickMessage(quickMessage: string): void {
  if (sendButtonDisabled.value) {
    sendQuickMessageUponTalloReply(quickMessage);
    return;
  }

  inputText.value = quickMessage;
  sendMessage();
}

function sendQuickMessageUponTalloReply(quickMessage: string): void {
  if (userWantsToSendMessageUponReply.value) {
    return;
  }

  userWantsToSendMessageUponReply.value = true;
  inputText.value = quickMessage;
}

async function sendMessage(): Promise<void> {
  if (sendButtonDisabled.value) {
    return;
  }

  if (messages.value.length === 0) {
    alerts.error('Convo is not yet created, something is wrong, debug, mate!');
    return;
  }

  const message = inputText.value.trim();

  if (message) {
    messages.value.push({ text: message, isSystem: false, id: null });
    inputText.value = '';
    clearMessageFromLocalStorage();
    saveRecentMessage(message);

    await nextTick();
    scrollChatToBottom();

    await devToolsStore.sendNewConvoMessage(message);

    if (userWantsToSendMessageUponReply.value) {
      userWantsToSendMessageUponReply.value = false;
      await sendMessage();
    }
  }
}

function sendUponTalloReply(): void {
  if (userWantsToSendMessageUponReply.value) {
    return;
  }

  userWantsToSendMessageUponReply.value = true;
}

function onInputEnterPress(): void {
  if (sendWhenTalloRespondsShown.value) {
    sendUponTalloReply();
  } else {
    sendMessage();
  }
}

function scrollChatToBottom(): void {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
  }
}

async function copyDialogForAI(): Promise<void> {
  const formattedDialog = messages.value
    .map((message) => {
      const speaker = message.isSystem ? 'AI' : 'Renter';
      return `- ${speaker}: ${message.text}`;
    })
    .join('\n');

  try {
    await navigator.clipboard.writeText(formattedDialog);
    alerts.success('Dialog copied to clipboard for AI debugging');
  } catch (error) {
    alerts.error('Failed to copy dialog to clipboard');
  }
}

onKeyStroke('Escape', () => {
  devToolsStore.clearSelectedMessages();
});

onKeyStroke('Backspace', (event: KeyboardEvent) => {
  // Delete last message on "CMD + OPTION + BACKSPACE"
  if (event.metaKey && event.altKey) {
    const lastMessage = messages.value[messages.value.length - 1];
    if (lastMessage?.id) {
      devToolsStore.deleteSingleMessage(lastMessage);
    } else {
      alerts.error('Cannot delete it right now, because message is not yet saved in DB.');
    }
  }
});

watch(
  () => devToolsStore.getConvoMessages,
  () => {
    messages.value = devToolsStore.getConvoMessages;

    nextTick(() => {
      scrollChatToBottom();
    });
  },
  { immediate: true },
);

watch(
  () => devToolsStore.sendingMessage,
  (newValue) => {
    if (newValue) {
      nextTick(() => {
        scrollChatToBottom();
      });
    }
  },
);
</script>

<style lang="scss" scoped>
.dev-tools-chat-component {
  border: 1px solid var(--t-color-neutral-700);
  border-radius: var(--t-border-radius-md);
  width: 45rem;
  max-height: 80vh;
}

.bubbles-window {
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  padding: var(--t-spacing-md);
  gap: var(--t-spacing-sm);
  flex-grow: 2;
}

.chat-bubble {
  max-width: 70%;
  border-radius: var(--t-border-radius-md);
  word-wrap: break-word;
  cursor: pointer;
}

.chat-bubble.incoming {
  background-color: var(--t-color-neutral-300);
  align-self: flex-start;
}

.chat-bubble.outgoing {
  background-color: var(--t-color-blue-500);
  color: var(--t-color-neutral-white);
  align-self: flex-end;
}

.chat-bubble.selected {
  color: var(--t-color-neutral-900);
  background-color: var(--t-color-red-300);
}

.chat-input {
  border-top: 1px solid var(--t-color-neutral-700);
}

.input {
  flex-grow: 2;
}

.quick-messages {
  max-width: 14rem;
}

.send-button {
  min-width: 11.25rem;
}

.blinking {
  animation: blink 1.5s infinite;
}

@keyframes blink {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.4;
  }
}
</style>
